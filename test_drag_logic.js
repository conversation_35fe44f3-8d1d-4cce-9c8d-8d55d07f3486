// 测试拖拽逻辑的修复
// 模拟修复前后的逻辑

console.log('=== 测试拖拽逻辑修复 ===');

// 模拟一个空的图片区域
const emptyImageArea = {
  type: 1, // 图片类型
  areaConfig: { name: 'images' }, // 空区域，有areaConfig
  path: '',
  sm_name: '',
  clientKey: 'empty-image-area-123'
};

// 模拟一个已经有1个图片的区域（非空区域）
const targetAreaWithImage = {
  type: 1, // 图片类型
  areaConfig: undefined, // 不是空区域，所以areaConfig为undefined
  path: '/some/image.jpg',
  sm_name: '现有图片',
  clientKey: 'existing-image-123',
  multiFiles: undefined // 还没有多文件列表
};

// 模拟拖拽的新图片素材
const draggedImageMaterial = {
  type: 1, // 图片类型
  path: '/new/image.jpg',
  name: '新图片',
  id: 456
};

// 修复前的逻辑（错误）
function getAreaTypeOld (targetArea) {
  return targetArea.areaConfig ? targetArea.areaConfig.name : (targetArea.type === 1 ? '图片区域' : targetArea.type === 2 ? '视频区域' : '其他区域');
}

function shouldAddToCarouselOld (areaType, materialType) {
  // 这里是问题所在：检查的是中文字符串，但实际的areaConfig.name是英文
  return (areaType === '图片区域' && materialType === 1) || (areaType === '视频区域' && materialType === 2);
}

// 模拟空区域的拖拽检查（修复前）
function canDropToEmptyAreaOld (areaType, materialType) {
  if (areaType === '图片区域' && materialType === 1) return true;
  else if (areaType === '视频区域' && materialType === 2) return true;
  else if (areaType === '网页区域') return true;
  return false;
}

// 修复后的逻辑（正确）
function getAreaTypeNew (targetArea) {
  return targetArea.areaConfig ? targetArea.areaConfig.name : (targetArea.type === 1 ? 'images' : targetArea.type === 2 ? 'videos' : '其他区域');
}

function shouldAddToCarouselNew (areaType, materialType) {
  return (areaType === 'images' && materialType === 1) || (areaType === 'videos' && materialType === 2);
}

// 模拟空区域的拖拽检查（修复后）
function canDropToEmptyAreaNew (areaType, materialType) {
  if (areaType === 'images' && materialType === 1) return true;
  else if (areaType === 'videos' && materialType === 2) return true;
  else if (areaType === 'webview') return true;
  return false;
}

// 测试修复前的逻辑
console.log('\n--- 修复前的逻辑 ---');
const areaTypeOld = getAreaTypeOld(targetAreaWithImage);
console.log('区域类型:', areaTypeOld);
const shouldAddOld = shouldAddToCarouselOld(areaTypeOld, draggedImageMaterial.type);
console.log('是否应该添加到轮播:', shouldAddOld);
console.log('结果:', shouldAddOld ? '添加（正确）' : '替换（错误）');

// 测试修复后的逻辑
console.log('\n--- 修复后的逻辑 ---');
const areaTypeNew = getAreaTypeNew(targetAreaWithImage);
console.log('区域类型:', areaTypeNew);
const shouldAddNew = shouldAddToCarouselNew(areaTypeNew, draggedImageMaterial.type);
console.log('是否应该添加到轮播:', shouldAddNew);
console.log('结果:', shouldAddNew ? '添加（正确）' : '替换（错误）');

// 模拟完整的条件判断逻辑
console.log('\n--- 完整条件判断测试 ---');
function testFullLogic (targetArea, materialData, isOldLogic = false) {
  // 首先检查类型是否匹配
  if (targetArea.type === materialData.type) {
    // 检查是否是图片区域或视频区域，支持多个文件
    const areaType = isOldLogic ?
      getAreaTypeOld(targetArea) :
      getAreaTypeNew(targetArea);

    const shouldAdd = isOldLogic ?
      shouldAddToCarouselOld(areaType, materialData.type) :
      shouldAddToCarouselNew(areaType, materialData.type);

    return {
      typeMatch: true,
      areaType: areaType,
      shouldAdd: shouldAdd,
      action: shouldAdd ? 'ADD_TO_CAROUSEL' : 'REPLACE'
    };
  } else {
    return {
      typeMatch: false,
      action: 'TYPE_MISMATCH_ERROR'
    };
  }
}

const resultOld = testFullLogic(targetAreaWithImage, draggedImageMaterial, true);
console.log('修复前完整逻辑:', resultOld);

const resultNew = testFullLogic(targetAreaWithImage, draggedImageMaterial, false);
console.log('修复后完整逻辑:', resultNew);

// 测试空区域的情况
console.log('\n--- 测试空区域拖拽 ---');
console.log('空图片区域类型:', emptyImageArea.areaConfig.name);
console.log('修复前 - 能否拖拽到空图片区域:', canDropToEmptyAreaOld(emptyImageArea.areaConfig.name, draggedImageMaterial.type));
console.log('修复后 - 能否拖拽到空图片区域:', canDropToEmptyAreaNew(emptyImageArea.areaConfig.name, draggedImageMaterial.type));

// 测试视频的情况
console.log('\n--- 测试视频区域 ---');
const targetAreaWithVideo = {
  type: 2, // 视频类型
  areaConfig: undefined,
  path: '/some/video.mp4',
  sm_name: '现有视频',
  clientKey: 'existing-video-123'
};

const draggedVideoMaterial = {
  type: 2, // 视频类型
  path: '/new/video.mp4',
  name: '新视频',
  id: 789
};

const videoAreaTypeOld = getAreaTypeOld(targetAreaWithVideo);
const videoShouldAddOld = shouldAddToCarouselOld(videoAreaTypeOld, draggedVideoMaterial.type);
console.log('修复前 - 视频区域类型:', videoAreaTypeOld, '是否添加:', videoShouldAddOld);

const videoAreaTypeNew = getAreaTypeNew(targetAreaWithVideo);
const videoShouldAddNew = shouldAddToCarouselNew(videoAreaTypeNew, draggedVideoMaterial.type);
console.log('修复后 - 视频区域类型:', videoAreaTypeNew, '是否添加:', videoShouldAddNew);

console.log('\n=== 测试完成 ===');
